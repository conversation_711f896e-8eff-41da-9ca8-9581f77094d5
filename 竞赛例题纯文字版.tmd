竞赛说明
一、项目背景
党的二十大报告指出，要加快建设制造强国、数字中国，推动制造业高端化、智能化、绿色化发展。《IDC中国制造企业调研报告，2021》报告指出，制造执行系统（MES，Manufacturing Execution System）是未来两年制造企业最优先投资的应用软件系统之一。
MES是智能制造的基础、核心和灵魂，它覆盖了整个智能制造的生产过程，与制造企业的各项业务紧密相连，可以为企业提供包括制造数据管理、计划排程管理、生产调度管理、库存管理、质量管理、人力资源管理、工作中心/设备管理、工具工装管理、采购管理、成本管理、项目看板管理、生产过程控制、底层数据集成分析、上层数据集成分解等管理模块，为企业打造一个扎实、可靠、全面、可行的制造协同管理平台。
A公司是一家汽车零部件生产的中小型传统制造厂商，计划上线MES信息化管理系统，一是解决由于缺乏生产信息化管理系统，导致面临交货周期计算不准确、交货周期长、生产订单跟踪不到位、生产计划难以控制等问题；二是按照政府文件要求，实现与Q市双碳管理大数据平台进行对接。
具体要求：
1.实现智能制造的过程控制、任务派工、质量管理、数据采集等；
2.实时监测每个设备的能源消耗数据，进行能源分析，实现能源利用效率的优化。
二、竞赛内容
比赛时间为4小时，考核“系统需求分析”“软件系统开发”和“系统部署测试”三个模块，具体如下表所示：
模块编号	模块名称	竞赛时间	分数
一	系统需求分析	4小时	25分
二	软件系统开发		55分
三	系统部署测试		20分
合计	4小时	100分

三、竞赛成果物提交
参赛选手根据分配的账号登录系统，在竞赛结束前将模块一、模块二、模块三的成果上传并提交到平台。
各模块在按照要求提交到平台的同时也将成果物拷贝到U盘中。
四、竞赛注意事项
提交、部署的文档、原型、代码等资源内容中不能填写与选手相关的信息，如赛位号、姓名和院校。如出现上述标记，本模块成绩按照零分处理。
 
模块一：系统需求分析
一、模块考核点
模块分值：25分
本模块重点考查参赛选手熟练收集、分析和归纳客户需求，清晰梳理业务流程，编制规范的需求规格说明书，熟练使用UI设计软件进行产品UI/UE设计，掌握正确的UI配色方案，设计出符合业务逻辑和人体工学的软件需求分析作品的能力。
二、任务要求
1.根据给定的竞赛任务需求说明，利用“需求规格说明书（模板）.docx”和相关工具软件（如，Visio等），编写模块概要简述，绘制对应业务流程图/活动图、用例图、类图、时序图、E-R图，完成“需求规格说明书.docx”文档编写。
2.利用原型设计工具Axure RP创建项目“产品原型.rp”，根据给定的竞赛任务需求说明，使用原型设计工具Axure和图片处理软件Adobe Photoshop进行软件原型设计，使之符合UI设计规范，同时实现原型界面之间交互的功能。
（1）裁判以Axure RP工具的Publish——Preview方式运行软件原型，评判作品。
（2）软件原型绘制时，页面命名规范，页面宽度1024px，高度不限。页面版式布局合理、美观，内容完整；同样功能请复用样式，避免一种功能、多种样式的情况。
（3）原型要有交互设计内容，并进行交互标注，用户体验良好，符合人体工学操作逻辑。交互描述，填写对部件进行“交互动作”加“链接指向”进行的说明；标注可交互状态，填写对部件所有“交互状态”的说明，字符限制提示，填写对部件是否有字符限制或者具体如何限制的说明。用户体验良好。
3.竞赛结束前，选手将上述成果物“需求规格说明书.docx”和“产品原型.rp”两个文件添加到“系统需求分析.zip”压缩包内，并提交压缩包文件。
三、竞赛任务
参赛选手根据客户提供的任务需求描述，按照模块一的任务要求，完成“需求规格说明书.docx”和“产品原型.rp”设计任务。

任务1：我的任务—采购申请审核（6分）
【基本要求】
1.进入双碳管理系统主界面，点击左侧我的任务下的采购申请审核标签，进入采购申请审核管理页面。实现采购申请审核分页数据列表显示，分页数据列表显示内容应包括：编号、申请单号、申请类型、计划单号、补货单号、申请人、申请日期、申请状态、审核人，审核日期，审核状态、【详情】按钮。
2.点击【详情】按钮后弹出「审核采购申请」对话框，对话框中可修改内容包括：审核意见，修改完成后点击【通过】或【驳回】按钮可保存修改内容并刷新分页数据列表，若点击【取消】按钮可关闭对话框。
3.分页数据列表上方搜索框中输入申请单号、申请类型下拉选（分类包括：计划申请、补货申请）、计划单号、申请人下拉选、申请日期（区间选择）、申请状态下拉选（分类包括：未提交、待审核、已审核）、审核人下拉选、审核日期（区间选择）、审核状态下拉选（分类包括：通过、驳回）后点击【搜索】按钮，可实现按搜索条件查询的功能并刷新分页列表数据，若点击【重置】按钮可清空输入的查询条件。

任务2：我的任务—销售合同审核（6分）
【基本要求】
1.进入双碳管理系统主界面，点击左侧我的任务下的销售合同审核标签，进入销售合同审核管理页面。实现销售合同审核分页数据列表显示，分页数据列表显示内容应包括：编号、合同编号、订单编号、业务员、客户名称、交货日期、送货方式、合同金额、签订日期、申请人、申请时间、申请状态、审核人，审核时间，审核状态、【详情】按钮。
2.点击【详情】按钮后弹出「审核销售合同」对话框，对话框中可修改内容包括：审核意见，修改完成后点击【通过】或【驳回】按钮可保存修改内容并刷新分页数据列表，若点击【取消】按钮可关闭对话框。
3.分页数据列表上方搜索框中输入订单编号、客户名称下拉选、交货日期（区间选择）、签订日期（区间选择）、申请人下拉选、申请日期（区间选择）、申请状态下拉选（分类包括：未提交、待审核、已审核）、审核人下拉选、审核日期（区间选择）、审核状态下拉选（分类包括：通过、驳回）后点击【搜索】按钮，可实现按搜索条件查询的功能并刷新分页列表数据，若点击【重置】按钮可清空输入的查询条件。

任务3：制造执行MES—工厂建模（6分）
【基本要求】
1.进入双碳管理系统主界面，点击左侧制造执行MES下的工厂建模标签，进入工厂建模页面。实现工厂建模数据列表显示，数据列表显示内容应包括：编号、产品、生产线名称、【修改】和【删除】按钮。
2.点击【修改】按钮后弹出「修改工厂建模」对话框，对话框中可修改内容包括上下两半部分：
上半部分包括：生产线名称（*必填项）、产品（*必填项）、产品型号（不可编辑）、产品规格（不可编辑）、描述；点击【选择】按钮后，弹出「选择物料档案」对话框，对话框分为左右两半部分，左半部分为树形结构目录（根目录为：分类；一级目录为：零件、成品），右半部分数据列表信息包括：编号、物料编码、物料名称、型号、规格、单位、单价、类型、备注、【选择】按钮。点击【选择】按钮后将数据自动填写至工厂建模明细信息中。
下半部分产品建模明细信息包括：序号、设备、编码、型号、生产日期、生产批号、制造商、【添加】按钮和【删除】按钮。点击【添加】按钮后可添加一条待补全的数据。
点击【确定】按钮可新建工厂建模管理信息数据并提示“新建成功”字样，若点击【取消】按钮可关闭对话框。
3.数据列表上方搜索框中输入生产线名称后点击【搜索】按钮，可实现按搜索条件模糊查询的功能并刷新列表数据，若点击【重置】按钮可清空输入的查询条件。
4.数据列表上方显示【新建】按钮，点击【新建】按钮后，在弹出的「添加工厂建模」对话框中输入生产线名称（*必填项）、产品（*必填项）、产品型号（不可编辑）、产品规格（不可编辑）、描述并添加设备后点击【确定】按钮可新建工厂建模数据并提示“新建成功”字样，若点击【取消】按钮可关闭对话框。

任务4：制造执行MES—项目维护（7分）
【基本要求】
1.进入双碳管理系统主界面，点击左侧制造执行MES下的项目维护标签，进入项目维护管理页面。实现项目维护管理数据列表显示，数据列表显示内容应包括：编号、项目名、负责人、开始日期、结束日期、项目状态、【修改】和【删除】按钮。
2.点击【修改】按钮后弹出「修改项目」对话框，对话框中可修改内容包括：项目名（*必填项）、负责人（*必填项）、开始日期（*必填项）、结束日期（*必填项）、项目状态下拉选（状态包括：挂起、正常）、项目介绍（图片上传），修改完成后点击【确定】按钮可保存修改内容并刷新数据列表，若点击【取消】按钮可关闭对话框。点击【删除】按钮可删除项目信息数据并提示“删除成功”字样。
3.数据列表上方搜索框中输入项目名、开始日期（区间选择）、结束日期（区间选择）、项目状态下拉选（状态包括：挂起、正常）后点击【搜索】按钮，可实现按搜索条件模糊查询的功能并刷新列表数据，若点击【重置】按钮可清空输入的查询条件。
4.数据列表上方显示【新建】按钮，点击【新建】按钮后，在弹出的「添加项目管理」对话框中输入项目名（*必填项）、负责人（*必填项）、开始日期（*必填项）、结束日期（*必填项）、项目状态下拉选（状态包括：挂起、正常）、项目介绍（图片上传）后点击【确定】按钮可新建项目信息数据并提示“新建成功”字样，若点击【取消】按钮可关闭对话框。

模块二：软件系统开发
一、模块考核点
模块分值：55分
本模块重点考查参赛选手的业务设计、前端页面开发和后端业务代码编写能力，具体包括：
1.前端页面开发。基于给定的系统需求，利用后端API提供的数据接口，使用HTML5、CSS3、JavaScript、Vue.js（ElementUI、vue-element-admin）等技术，遵循MVVM模式完成前端页面，实现业务功能，要求编码符合前端工程化开发技术规范。
2.后端业务开发。基于给定的系统需求，利用可视化开发工具设计数据库，并利用Spring Boot框架实现后端业务功能，完成RESTful API接口开发，并发布运行。要求设计符合Spring Boot框架的Domain/POJO、DAO、Service、Controller分层架构模式，编码符合命名和注释规范。
二、任务要求
1.利用数据库可视化管理工具，创建carbon数据库，并导入carbon.sql数据库文件，根据竞赛任务描述，实现项目业务功能后，导出数据库脚本carbon.sql。数据库账号/密码：root/123456。
2.利用后端开发工具IntelliJ IDEA，打开后端项目carbon，根据竞赛任务描述，实现项目业务功能，然后利用Maven将项目发布为carbon.jar包文件。
3.使用前端开发工具，打开管理前端项目，根据竞赛任务描述，实现项目业务功能，然后发布为生产环境dist1文件夹，使用Nginx进行部署，在浏览器内键入http://IP:8088，验证管理端的业务功能，利用admin/admin123登录双碳管理系统。
4.使用前端开发工具，打开用户前端项目，根据竞赛任务描述，实现项目业务功能，然后发布为生产环境dist2文件夹，使用Nginx进行部署，在浏览器内键入http://IP:8081，验证用户前端的业务功能。
5.使用前端开发工具，打开数据可视化前端项目，根据竞赛任务描述，实现项目业务功能，然后发布为生产环境dist3文件夹，使用Nginx进行部署，在浏览器内键入http://IP:8080，验证可视化前端的业务功能。
6.竞赛结束前，选手将上述成果物“carbon.sql”、“carbon.jar”、“dist1”、“dist2”和“dist3”添加到“应用系统开发.zip”压缩包内，并提交压缩包文件。
三、竞赛任务
参赛选手根据客户提供的任务描述，按照模块二的任务要求，完成管理前后端、用户前端、数据可视化功能开发任务。

任务1：我的任务—承运申请审核（10分）
注：选手自行编写管理前端界面。
【基本要求】
1.进入双碳管理系统主界面，点击左侧我的任务下的承运申请审核标签，进入承运申请审核管理页面。实现承运申请审核分页数据列表显示，分页数据列表显示内容应包括：编号、单据号、出库单号、发货单号、客户、收货地址、联系人、联系电话、承运人、运输距离、申请人、申请日期、申请状态、审核人，审核日期，审核状态、【详情】按钮。
2.点击【详情】按钮后弹出「查看承运申请」对话框，对话框中可修改内容包括：审核意见，修改完成后点击【通过】或【驳回】按钮可保存修改内容并刷新分页数据列表，若点击【取消】按钮可关闭对话框。
3.分页数据列表上方搜索框中输入单据号、出库单号、发货单号、客户下拉选、申请人下拉选、申请日期（区间选择）、申请状态下拉选（分类包括：未提交、待审核、已审核）、审核人下拉选、审核时间（区间选择）、审核状态下拉选（分类包括：通过、驳回）后点击【搜索】按钮，可实现按搜索条件查询的功能并刷新分页列表数据，若点击【重置】按钮可清空输入的查询条件。
注：接口详见双碳管理系统API文档。

任务2：供应链SCM—销售管理—销售发货（12分）
注：选手自行编写、设计服务接口部分（pojo、mapper、service、controller）、管理前台界面与数据库设计。
【基本要求】
1.进入双碳管理系统主界面，点击左侧供应链SCM下的销售管理下的销售发货标签，进入销售发货管理页面。实现销售发货管理数据列表分页显示，数据列表显示内容应包括：编号、发货编号、合同编号、客户名称、送货方式、交货日期、收货地址、联系人、联系电话、出库状态、创建时间、【修改】和【删除】按钮。
2.点击【修改】按钮后弹出「修改销售发货单」对话框，对话框中可修改内容包括：送货方式下拉选（包括：快递、物流）（*必填项）、交货日期（*必填项）、收货地址（*必填项）、联系人（*必填项）、联系电话（*必填项）、备注，修改完成后点击【确定】按钮可保存修改内容并刷新数据列表，若点击【取消】按钮可关闭对话框。点击【删除】按钮可弹出警告对话框，提示内容“是否确认删除销售发货单编号为xx的数据项？”，点击【确定】按钮后删除对应的销售发货单信息数据并提示“删除成功”字样，若点击【取消】按钮可关闭对话框。
3.数据列表上方搜索框中输入发货编号、合同编号、客户名称下拉选（客户名称菜单）、交货日期、出库状态下拉选（包括：未出库、出库中、已出库）、创建时间后点击【搜索】按钮，可实现按搜索条件模糊查询的功能并刷新列表数据，若点击【重置】按钮可清空输入的查询条件。
注：接口详见系统API文档。

任务3：采购管理系统—政策法规（11分）
注：选手自行编写用户前端界面。
【基本要求】
1.进入双碳招投标网主界面，点击页面上方政策法规菜单，进入政策法规列表页。实现政策法规数据列表显示，数据列表内容包括：固定icon，政策法规标题和发布时间。
2.点击数据项目，页面跳转至政策法规详情页。页面显示政策法规信息、发布时间和附件，点击附件名称进行下载。
注：接口详见系统API文档。

任务4：采购管理系统—参与投标（12分）
注：选手需对服务接口部分返回的数据进行整合、解析，并自行编写前端页面。
【基本要求】
1.进入双碳招投标网主界面，点击右上角个人头像，进入个人中心页。
2.点击个人中心页左侧【参与投标】菜单，进入参与投标页，实现可投标标的信息数据列表展示，信息数据列表内容包括：标的发布名称，投标结束时间和【报名】按钮；点击标的名称，在浏览器新的页签中，打开对应的招标公告；点击【报名】按钮提示“报名成功”字样，若点击【取消】按钮可关闭对话框。
注：接口详见系统API文档。


任务5：数据可视化（10分）
注：选手自行编写、设计服务接口部分（pojo、mapper、service、controller）、管理前台界面与数据库设计。
【基本要求】
可视化平台中，数据分别通过仪表盘、环状图、柱形图、曲线图、滚动表格等多种形式展示数据变化。
可视化平台大致分为左、中、右三部分，左侧由能耗总览、耗能占比、库存预警构成，中间由数据总览、销售计划完成率构成，右侧由销售统计、销售排名（TOP8）、生产统计构成。平台右上角动态显示当前系统日期、星期、时间，格式。
在管理端进行添加数据后，可视化图表进行相应变化。
1.能耗总览仪表盘，统计分析耗电量、耗水量、碳排放量的总数，以不同颜色的仪表盘形式展示耗电量、耗水量、碳排放量的总数，在环状图中间区域显示各数据的数据值。
2.能耗占比环状图，请分析各能耗的消耗数占能耗总数的比例，扇形图中包括两部分内容：能耗占比图中需显示办公耗电量、办公用水量、生产用水量、生产耗电量的数量及占能耗总数的比例。环状图应用不同颜色区分显示各个能耗的占比，中间区域需显示能耗总数。
3.库存预警滚动表格，表格中每行数据由编号、物料及物料名称、仓库及仓库名称、当前库存及库存数量、状态[包括正常（白色）、不足（绿色）、溢出（红色）]构成，表格数据会自动滚动。
4.数据总览分为上、下两部分：
上半部分第一行统计今年累计销售额、本月累计销售额、今日累计销售额，第二行统计本年累计碳排放、本月累计碳排放、今日累计碳排放。
下半部分以表格形式展示碳排放排行榜，根据碳排放从高到低显示8条碳排放较高的数据。表格中列顺序分别为编号、设备、生产数量、碳排放。

模块三：系统部署测试
一、模块考核点
模块分值：20分
本模块重点考查参赛选手的系统部署、功能测试、Bug排查修复及文档编写能力，具体包括：
1.系统部署。将给定项目发布到集成部署工具中，确保正常运行。
2.功能测试及Bug修复。使用给定的前后端源码，制订测试策略，设计测试用例，完成指定的功能测试；记录测试中出现的Bug，对Bug进行分析与修复；基于测试报告模板，撰写系统测试报告。
3.API接口测试。使用JMeter工具对后端RESTful API接口进行编码规范测试，输出API接口测试报告。
二、任务要求
1.项目准备
（1）利用数据库可视化管理工具，创建carbon数据库，并导入carbon.sql数据库文件。数据库账号/密码：root/123456。
（2）利用后端开发工具IntelliJ IDEA，打开后端项目carbon，并运行。
（3）使用前端开发工具，打开管理前端项目，并运行。利用admin/admin123登录双碳管理系统。
（4）使用前端开发工具，打开用户前端项目，并运行。
（5）使用前端开发工具，打开数据可视化前端项目，并运行。
2.基于待测系统，进行功能测试，并对指定Bug进行功能修正，完成“软件测试报告.docx”和“系统功能修正报告.docx”文档撰写。
3.基于待测系统，使用JMeter工具对后端RESTFul API进行全面测试。完成“软件测试报告.docx”文档撰写。
4.竞赛结束前，选手将上述成果物“软件测试报告.docx”和“软件功能修正说明.docx”添加到“系统部署测试.zip”压缩包内，并提交压缩包文件到竞赛管理平台上。
三、竞赛任务

任务1：功能测试及指定Bug修改（14分）
（一）功能测试
【任务要求】
根据“双碳管理系统功能测试范围.pdf”中描述的功能范围进行全范围测查，找出特定的至少5个Bug，根据找出的Bug进行缺陷分析，分析Bug出现的原因，并填写《系统测试报告模板》中的缺陷表格，缺陷表格样例见表1。
表1 缺陷分析表
缺陷编号	01
缺陷简要描述	车辆调度中搜索框内输入单据号后，点击【搜索】按钮没有反应。
缺陷重现步骤	1、用户输账号密码后登陆双碳管理系统；
2、智能仓储WMS中点击运输管理下的车辆调度标签，进入车辆管理页面；
3、输入单据号后点击【搜索】按钮，无法加载对应数据，点击无反应。
缺陷验证程度	严重      缺陷等级：（致命、严重、一般、提示）
涉及功能模块	智能仓储—运输管理—车辆管理
缺陷分析原因	查询接口异常
缺陷功能截图	略

（二）指定Bug修改
修正下面功能Bug，并完成“系统功能修正报告.docx”文档撰写。
表2 系统功能修正报告样例
Bug编号	001
Bug修正截图	略
修改文件名称及对应代码	文件名称：XXX.java
修正代码如下：
略
（1）管理端货主管理模块删除功能点击后无任何反应
进入双碳管理系统主界面，点击“智能仓储管理系统WMS”后再点击左侧基础数据下的货主标签，进入货主管理界面。
【基本要求】
注：选手需根据描述的功能缺陷进行改错，并修正对应错误。
点击【删除】按钮后应删除数据并刷新页面，但当前页面点击【删除】后无任何反应，请查找对应错误并改正。
（2）产品统计中供应商下拉选择无法查看对应数据
进入双碳管理系统主界面，点击“供应链SCM”后再点击左侧采购报表下的产品统计标签，进入产品统计管理界面。
【基本要求】
注：选手需根据描述的功能缺陷进行改错，并修正对应错误。
产品统计页面应包括：合同编号、供应商、物料名称、型号、规格、单位、单价、采购数量、金额、签约日期，点击搜索功能区中的供应商下拉选择后，应显示所有供应商名称并可以按照选择的供应商进行查询，但现在供应商下拉选中无法显示对应的供应商名称数据，请查找对应错误并改正。
（3）销售发退货统计点击tab栏中的月份统计标签无法显示数据列表内容
进入双碳管理系统主界面，点击“供应链SCM”后再点击左侧统计分析下的销售发退货统计标签，进入销售发退货统计管理界面。
【基本要求】
注：选手需根据描述的功能缺陷进行改错，并修正对应错误。
点击销售发退货统计数据列表中的【月份统计】标签应显示对应数据列表内容，内容包括：月份、物料名称、已收金额、退款金额、订货数量、发货数量、退货数量，现在点击【月份统计】按钮后可以切换月份统计页面，但无法显示月份统计数据列表信息，请查找对应错误并改正。
（4）承运申请中客户下拉选择无法查看对应数据
进入双碳管理系统主界面，点击“智能仓储WMS”后再点击左侧运输管理下的承运申请标签，进入承运申请管理界面。
【基本要求】
注：选手需根据描述的功能缺陷进行改错，并修正对应错误。
承运申请页面点击搜索功能区中的客户下拉选择后，应显示所有客户名称并可以按照选择的客户进行查询，但现在客户下拉选择中无法显示对应的客户名称数据，请查找对应错误并改正。
（5）库存明细报表中点击【导出】按钮后无法将对应数据导出为excel文件
进入双碳管理系统主界面，点击“智能仓储WMS”后再点击左侧报表管理下的库存明细报表标签，进入库存明细报表管理界面。
【基本要求】
注：选手需根据描述的功能缺陷进行改错，并修正对应错误。
点击【导出】按钮后应弹出“是否确认导出”对话框，点击【确认】按钮后将库存明细报表数据导出为excel文件，点击【取消】按钮后关闭对话框，但现在点击【导出】按钮后无任何反应，请查找对应错误并改正。

任务2：API接口测试（6分）
通过JMeter测试工具，根据系统测试范围测试系统API，根据《系统测试报告模板.docx》撰写软件测试报告。

